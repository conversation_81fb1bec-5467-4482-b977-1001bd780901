import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, RotateCcw } from 'lucide-react';
import { riasecQuestions } from '../../data/assessmentQuestions';
import ProgressBar from '../UI/ProgressBar';

const RiasecAssessment = ({ data, onUpdate, isActive, showQuickNavigation = true }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState(data || {});

  // Load saved answers from localStorage on component mount
  useEffect(() => {
    const savedAnswers = localStorage.getItem('riasecAssessmentAnswers');
    if (savedAnswers && Object.keys(data || {}).length === 0) {
      try {
        const parsedAnswers = JSON.parse(savedAnswers);
        setAnswers(parsedAnswers);
      } catch (error) {
        console.error('Error loading saved RIASEC answers:', error);
      }
    }
  }, [data]);

  // Save answers to localStorage whenever answers change
  useEffect(() => {
    if (Object.keys(answers).length > 0) {
      localStorage.setItem('riasecAssessmentAnswers', JSON.stringify(answers));
    }
  }, [answers]);

  // Flatten all questions with category info
  const allQuestions = [];
  Object.entries(riasecQuestions.categories).forEach(([categoryKey, category]) => {
    category.questions.forEach((question, index) => {
      allQuestions.push({
        id: `${categoryKey}_${index}`,
        text: question,
        category: categoryKey,
        categoryName: category.name
      });
    });
  });

  const totalQuestions = allQuestions.length;
  const currentQuestion = allQuestions[currentQuestionIndex];
  const answeredCount = Object.keys(answers).length;
  const progress = (answeredCount / totalQuestions) * 100;
  const isComplete = answeredCount === totalQuestions;

  useEffect(() => {
    if (isActive) {
      // Calculate scores and update parent
      const scores = calculateCategoryScores();
      onUpdate(scores, isComplete);
    }
  }, [answers, isComplete, isActive]);

  // Populate sidebar navigation when component is active
  useEffect(() => {
    if (isActive && !showQuickNavigation) {
      const container = document.getElementById('quick-navigation-container');
      if (container) {
        const navigationHTML = Object.entries(riasecQuestions.categories).map(([categoryKey, category]) => {
          const categoryQuestions = allQuestions.filter(q => q.category === categoryKey);
          const categoryAnswered = categoryQuestions.filter(q => answers[q.id]).length;
          const categoryProgress = (categoryAnswered / categoryQuestions.length) * 100;

          return `
            <button
              class="w-full text-left p-3 rounded-lg border border-slate-200 hover:border-slate-300 transition-all"
              onclick="window.navigateToRiasecCategory('${categoryKey}')"
            >
              <div class="text-sm font-medium text-slate-900 mb-1">${category.name}</div>
              <div class="text-xs text-slate-600 mb-2">
                ${categoryAnswered}/${categoryQuestions.length} answered
              </div>
              <div class="w-full bg-slate-200 rounded-full h-1">
                <div
                  class="bg-blue-500 h-1 rounded-full transition-all"
                  style="width: ${categoryProgress}%"
                ></div>
              </div>
            </button>
          `;
        }).join('');

        container.innerHTML = navigationHTML;

        // Set up navigation function
        window.navigateToRiasecCategory = (categoryKey) => {
          const firstQuestionIndex = allQuestions.findIndex(q => q.category === categoryKey);
          setCurrentQuestionIndex(firstQuestionIndex);
        };
      }
    }
  }, [isActive, showQuickNavigation, answers, allQuestions, setCurrentQuestionIndex]);

  const calculateCategoryScores = () => {
    const categoryScores = {};
    
    Object.keys(riasecQuestions.categories).forEach(categoryKey => {
      const categoryAnswers = Object.entries(answers)
        .filter(([questionId]) => questionId.startsWith(categoryKey))
        .map(([, answer]) => answer);
      
      if (categoryAnswers.length > 0) {
        const average = categoryAnswers.reduce((sum, answer) => sum + answer, 0) / categoryAnswers.length;
        // Convert from 1-5 scale to 0-100 scale
        categoryScores[categoryKey] = Math.round(((average - 1) / 4) * 100);
      } else {
        categoryScores[categoryKey] = 0;
      }
    });
    
    return categoryScores;
  };

  const handleAnswer = (value) => {
    const newAnswers = {
      ...answers,
      [currentQuestion.id]: value
    };
    setAnswers(newAnswers);
  };

  const goToNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const resetAssessment = () => {
    setAnswers({});
    setCurrentQuestionIndex(0);
    // Clear localStorage
    localStorage.removeItem('riasecAssessmentAnswers');
  };

  if (!currentQuestion) return null;

  return (
    <div className="p-6 md:p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-slate-900">{riasecQuestions.title}</h2>
            <p className="text-slate-600 mt-1">{riasecQuestions.description}</p>
          </div>
          <button
            onClick={resetAssessment}
            className="flex items-center text-slate-500 hover:text-slate-700 transition-colors"
            title="Reset Assessment"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Reset
          </button>
        </div>
        
        <div className="flex items-center justify-between text-sm text-slate-600 mb-2">
          <span>Question {currentQuestionIndex + 1} of {totalQuestions}</span>
          <span>{answeredCount} answered ({Math.round(progress)}% complete)</span>
        </div>
        
        <ProgressBar progress={progress} className="mb-4" />
      </div>

      {/* Question */}
      <motion.div
        key={currentQuestionIndex}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
        className="mb-8"
      >
        <div className="bg-slate-50 rounded-lg p-6 mb-6">
          <div className="text-sm text-green-600 font-medium mb-2">
            {currentQuestion.categoryName}
          </div>
          <h3 className="text-lg font-medium text-slate-900 leading-relaxed">
            {currentQuestion.text}
          </h3>
        </div>

        {/* Answer Options */}
        <div className="space-y-3">
          {riasecQuestions.scale.map((option) => (
            <label
              key={option.value}
              className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                answers[currentQuestion.id] === option.value
                  ? 'border-green-500 bg-green-50'
                  : 'border-slate-200 hover:border-slate-300 bg-white'
              }`}
            >
              <input
                type="radio"
                name={`question_${currentQuestionIndex}`}
                value={option.value}
                checked={answers[currentQuestion.id] === option.value}
                onChange={() => handleAnswer(option.value)}
                className="sr-only"
              />
              <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                answers[currentQuestion.id] === option.value
                  ? 'border-green-500 bg-green-500'
                  : 'border-slate-300'
              }`}>
                {answers[currentQuestion.id] === option.value && (
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <span className="text-slate-900">{option.label}</span>
                  <span className="text-sm text-slate-500 ml-2">{option.value}</span>
                </div>
              </div>
            </label>
          ))}
        </div>
      </motion.div>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <button
          onClick={goToPrevious}
          disabled={currentQuestionIndex === 0}
          className={`flex items-center px-4 py-2 rounded-lg transition-all ${
            currentQuestionIndex === 0
              ? 'text-slate-400 cursor-not-allowed'
              : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
          }`}
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </button>

        <div className="text-center">
          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
            isComplete
              ? 'bg-emerald-100 text-emerald-800'
              : 'bg-slate-100 text-slate-600'
          }`}>
            {isComplete ? 'Complete' : `${answeredCount}/${totalQuestions} answered`}
          </div>
        </div>

        <button
          onClick={goToNext}
          disabled={currentQuestionIndex === totalQuestions - 1}
          className={`flex items-center px-4 py-2 rounded-lg transition-all ${
            currentQuestionIndex === totalQuestions - 1
              ? 'text-slate-400 cursor-not-allowed'
              : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
          }`}
        >
          Next
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>

      {/* Quick Navigation - Only show if showQuickNavigation is true */}
      {showQuickNavigation && (
        <div className="mt-8 pt-6 border-t border-slate-200">
          <h4 className="text-sm font-medium text-slate-900 mb-3">Quick Navigation by Category</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {Object.entries(riasecQuestions.categories).map(([categoryKey, category], index) => {
              const categoryQuestions = allQuestions.filter(q => q.category === categoryKey);
              const categoryAnswered = categoryQuestions.filter(q => answers[q.id]).length;
              const categoryProgress = (categoryAnswered / categoryQuestions.length) * 100;

              return (
                <button
                  key={categoryKey}
                  onClick={() => {
                    const firstQuestionIndex = allQuestions.findIndex(q => q.category === categoryKey);
                    setCurrentQuestionIndex(firstQuestionIndex);
                  }}
                  className="text-left p-3 rounded-lg border border-slate-200 hover:border-slate-300 transition-all"
                >
                  <div className="text-sm font-medium text-slate-900 mb-1">{category.name}</div>
                  <div className="text-xs text-slate-600 mb-2">
                    {categoryAnswered}/{categoryQuestions.length} answered
                  </div>
                  <div className="w-full bg-slate-200 rounded-full h-1">
                    <div
                      className="bg-green-500 h-1 rounded-full transition-all"
                      style={{ width: `${categoryProgress}%` }}
                    ></div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default RiasecAssessment;
